# NFC项目结构文档

## 项目根目录
- `.dart_tool/`: Dart工具相关配置和缓存
- `.flutter-plugins`: Flutter插件配置文件
- `.flutter-plugins-dependencies`: Flutter插件依赖文件
- `.gitignore`: Git忽略规则文件
- `.idea/`: IntelliJ IDEA项目配置
- `.metadata`: Flutter项目元数据
- `.vscode/`: VS Code编辑器配置
- `README.md`: 项目说明文档
- `analysis_options.yaml`: Dart静态分析配置
- `android/`: Android平台相关代码
- `assets/`: 静态资源文件(图片、字体、视频等)
- `build/`: 构建输出目录
- `flutter/`: Flutter框架相关文件
- `flutter_launcher_icons.yaml`: 应用图标配置
- `ios/`: iOS平台相关代码
- `lib/`: Dart主代码目录
- `linux/`: Linux平台相关代码
- `nfc_project.iml`: IntelliJ项目文件
- `pubspec.lock`: 依赖锁定文件
- `pubspec.yaml`: 项目依赖配置文件
- `test/`: 测试代码目录
- `windows/`: Windows平台相关代码

## 详细目录说明

### android/
Android平台特定代码:
- `app/`: Android应用模块
- `build.gradle.kts`: 项目构建配置
- `gradle/`: Gradle包装器
- `gradlew`: Gradle执行脚本
- `local.properties`: 本地SDK路径配置

### assets/
静态资源文件:
- 字体文件(.ttf)
- 图片(.png, .jpg)
- 视频文件(.mp4)

### lib/
Dart主代码:
- `main.dart`: 应用入口文件
- `flutter/`: Flutter相关文档

### test/
测试代码:
- `widget_test.dart`: 小部件测试

### 其他
- `pubspec.yaml`: 定义项目依赖和元数据
- `flutter_launcher_icons.yaml`: 配置应用图标