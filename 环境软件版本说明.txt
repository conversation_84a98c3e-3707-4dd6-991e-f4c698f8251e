# NFC项目环境软件版本说明

## 基础环境

### Flutter环境
- Flutter版本: 3.29.2 (channel stable)
- Dart版本: 3.7.2
- DevTools版本: 2.42.3
- Flutter SDK路径: https://github.com/flutter/flutter.git
- Flutter引擎版本: 18b71d647a
- 安装目录: C:\Development\flutter

### Java环境
- Java版本: Java 17.0.12 LTS (2024-07-16)
- Java运行环境: Java(TM) SE Runtime Environment (build 17.0.12+8-LTS-286)
- Java虚拟机: Java HotSpot(TM) 64-Bit Server VM (build 17.0.12+8-LTS-286)
- 安装目录: C:\Development\Java\jdk-17

### Android Studio环境
- Android Studio版本: 2024.3
- Java版本(Android Studio内置): OpenJDK Runtime Environment (build 21.0.5+-12932927-b750.29)
- 安装目录: C:\Development\Android Studio

### Android SDK环境
- Android SDK安装目录: C:\Users\<USER>\AppData\Local\Android\sdk
- Android NDK版本: 27.0.12077973
- Android NDK安装目录: C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973

## 项目配置

### Gradle配置
- Gradle版本: 8.9
- 构建工具: Gradle Kotlin DSL (build.gradle.kts)

### Android配置
- 编译SDK版本(compileSdk): Flutter compileSdkVersion
- 最小SDK版本(minSdk): 26
- 目标SDK版本(targetSdk): Flutter targetSdkVersion
- NDK版本: 27.0.12077973
- 应用ID: com.example.nfc_project
- Java兼容性: JavaVersion.VERSION_1_8
- Kotlin JVM目标: JavaVersion.VERSION_1_8

### 签名配置
- 签名文件: upload-keystore.jks
- 密钥别名: upload
- 密钥密码: 123456
- 存储密码: 123456

### 构建配置
- 发布构建启用代码混淆(minifyEnabled): true
- 使用ProGuard规则文件: proguard-android.txt, proguard-rules.pro

## 项目依赖

### Flutter SDK
- SDK版本要求: ^3.7.2

### 主要依赖包
- cupertino_icons: ^1.0.8
- flutter_nfc_kit: ^3.4.2
- flutter_launcher_icons: ^0.14.3
- video_player: ^2.8.2
- lunar: ^1.6.1
- table_calendar: ^3.0.9
- material_color_utilities: 0.11.1

### 开发依赖
- flutter_lints: ^5.0.0
- async: ^2.12.0
- fake_async: 1.3.2
- leak_tracker: 10.0.8
- vm_service: 14.3.1

## 注意事项

1. 本项目使用Gradle 8.9版本，相比早期的7.5.1版本有较大变化，请确保使用兼容的Gradle插件。
2. 项目使用Java 8兼容性设置，但开发环境使用Java 17/21，这是Flutter项目的常见配置。
3. 签名配置已设置在build.gradle.kts文件中，用于生成发布版本的APK。
4. 项目最低支持Android 8.0 (API 26)及以上版本。
5. 构建时启用了代码混淆，确保应用安全性和减小APK体积。