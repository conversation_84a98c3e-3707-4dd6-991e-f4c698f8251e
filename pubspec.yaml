name: nfc_project
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  material_color_utilities: 0.11.1
  vector_math: ^2.1.4  # 添加这一行
  
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_nfc_kit: ^3.4.2
  flutter_launcher_icons: ^0.14.3
  video_player: ^2.8.2
  lunar: ^1.6.1
  table_calendar: ^3.0.9
  csv: ^6.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  async: ^2.12.0
  fake_async: ^1.3.3
  leak_tracker: ^11.0.1
  vm_service: ^15.0.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/MP4/
    - assets/wp001.png
    - assets/wp002.png
    - assets/wp003.png
    - assets/wp004.png
    - assets/wp005.png
    - assets/wp006.png
    - assets/logo00.jpg
    - assets/logo01.jpg
    - assets/logo02.jpg
    - assets/logo03.jpg
    - assets/logo04.jpg
    - assets/logo05.jpg
    - assets/logo06.jpg
    - assets/tibetan_calendar_all.csv
    - assets/汉传佛教节日.csv
    - assets/sutras.json
    - assets/figure of the Buddha/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font.
  fonts:
    - family: SimKai
      fonts:
        - asset: assets/simkai.ttf
    
    - family: hyxk
      fonts:
        - asset: assets/hyxk.ttf
          # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

