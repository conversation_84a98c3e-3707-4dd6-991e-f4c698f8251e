import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:video_player/video_player.dart';
import 'package:lunar/lunar.dart';
import 'package:table_calendar/table_calendar.dart';
import 'tibetan_calendar.dart';

void main() {
  runApp(const MyApp());
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final List<String> _splashImages = [
    'assets/logo00.jpg',
    'assets/logo01.jpg',
    'assets/logo02.jpg',
    'assets/logo03.jpg',
    'assets/logo04.jpg',
    'assets/logo05.jpg',
    'assets/logo06.jpg',
  ];
  
  late String _selectedImage;

  @override
  void initState() {
    super.initState();
    _selectedImage = _splashImages[DateTime.now().millisecondsSinceEpoch % _splashImages.length];
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    Timer(const Duration(seconds: 3), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const MyHomePage(title: '般若梵心')),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          Positioned.fill(
            child: Image.asset(
              _selectedImage,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }
}

// 商城页面
class ShopPage extends StatefulWidget {
  const ShopPage({super.key});

  @override
  State<ShopPage> createState() => _ShopPageState();
}

class _ShopPageState extends State<ShopPage> {
  int _selectedCategoryIndex = 0;
  final List<String> _categories = ['手串', '佛牌', '佛卡', '香品', '法器'];
  
  final Map<String, List<Map<String, dynamic>>> _products = {
    '手串': [
      {
        'name': '紫檀木手串',
        'price': 168.0,
        'image': 'assets/般若凡品01.png',
        'description': '天然紫檀木制作，质地坚硬，纹理美观，佩戴有助于静心修行。',
        'stock': 15
      },
      {
        'name': '菩提子手串',
        'price': 88.0,
        'image': 'assets/般若凡品02.jpg',
        'description': '精选菩提子制作，寓意觉悟智慧，是修行者的理想选择。',
        'stock': 25
      },
      {
        'name': '沉香木手串',
        'price': 288.0,
        'image': 'assets/般若凡品03.png',
        'description': '珍贵沉香木制作，香气怡人，具有安神静心的功效。',
        'stock': 8
      },
      {
        'name': '水晶手串',
        'price': 128.0,
        'image': 'assets/般若凡品04.jpg',
        'description': '天然水晶制作，晶莹剔透，有助于净化心灵，提升正能量。',
        'stock': 20
      },
    ],
    '佛牌': [
      {
        'name': '观音菩萨佛牌',
        'price': 198.0,
        'image': 'assets/观音心咒1.jpg',
        'description': '观音菩萨护身佛牌，慈悲为怀，保佑平安吉祥。',
        'stock': 12
      },
      {
        'name': '文殊菩萨佛牌',
        'price': 188.0,
        'image': 'assets/观音心咒清晰.jpg',
        'description': '文殊菩萨智慧佛牌，开启智慧，学业有成。',
        'stock': 18
      },
      {
        'name': '地藏菩萨佛牌',
        'price': 178.0,
        'image': 'assets/logo05.jpg',
        'description': '地藏菩萨慈悲佛牌，消除业障，增福延寿。',
        'stock': 10
      },
    ],
    '佛卡': [
      {
        'name': '心经佛卡',
        'price': 38.0,
        'image': 'assets/微信图片_20250415104059.png',
        'description': '精美心经佛卡，随身携带，时刻感受佛法智慧。',
        'stock': 50
      },
      {
        'name': '大悲咒佛卡',
        'price': 42.0,
        'image': 'assets/微信图片_20250415104105.jpg',
        'description': '大悲咒佛卡，观音菩萨加持，消灾解厄。',
        'stock': 45
      },
      {
        'name': '金刚经佛卡',
        'price': 45.0,
        'image': 'assets/微信图片_20250415104109.jpg',
        'description': '金刚经佛卡，般若智慧，破除烦恼。',
        'stock': 40
      },
    ],
    '香品': [
      {
        'name': '檀香线香',
        'price': 68.0,
        'image': 'assets/所求如愿.png',
        'description': '天然檀香制作，香气清雅，适合日常供佛礼拜。',
        'stock': 30
      },
      {
        'name': '沉香盘香',
        'price': 158.0,
        'image': 'assets/金刚杵.png',
        'description': '珍贵沉香制作，香气持久，静心修行必备。',
        'stock': 15
      },
    ],
    '法器': [
      {
        'name': '金刚杵',
        'price': 388.0,
        'image': 'assets/金刚杵.png',
        'description': '精工制作金刚杵，密宗法器，降魔护法。',
        'stock': 5
      },
      {
        'name': '念珠',
        'price': 128.0,
        'image': 'assets/logo01.jpg',
        'description': '108颗念珠，助力念佛修行，功德无量。',
        'stock': 22
      },
    ],
  };

  List<Map<String, dynamic>> _cart = [];
  double _totalAmount = 0.0;

  void _addToCart(Map<String, dynamic> product) {
    setState(() {
      final existingIndex = _cart.indexWhere((item) => item['name'] == product['name']);
      if (existingIndex >= 0) {
        _cart[existingIndex]['quantity'] += 1;
      } else {
        _cart.add({
          ...product,
          'quantity': 1,
        });
      }
      _calculateTotal();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${product['name']} 已加入购物车'),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _calculateTotal() {
    _totalAmount = _cart.fold(0.0, (sum, item) => sum + (item['price'] * item['quantity']));
  }

  void _showCartDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('购物车', style: TextStyle(fontFamily: 'SimKai')),
              content: SizedBox(
                width: double.maxFinite,
                height: 300,
                child: _cart.isEmpty
                    ? const Center(
                        child: Text('购物车为空', style: TextStyle(fontFamily: 'SimKai')),
                      )
                    : ListView.builder(
                        itemCount: _cart.length,
                        itemBuilder: (context, index) {
                          final item = _cart[index];
                          return Card(
                            child: ListTile(
                              leading: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.asset(
                                  item['image'],
                                  width: 50,
                                  height: 50,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              title: Text(
                                item['name'],
                                style: const TextStyle(fontFamily: 'SimKai', fontSize: 14),
                              ),
                              subtitle: Text(
                                '¥${item['price'].toStringAsFixed(2)}',
                                style: const TextStyle(color: Colors.red),
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.remove),
                                    onPressed: () {
                                      setDialogState(() {
                                        if (item['quantity'] > 1) {
                                          item['quantity'] -= 1;
                                        } else {
                                          _cart.removeAt(index);
                                        }
                                        setState(() {
                                          _calculateTotal();
                                        });
                                      });
                                    },
                                  ),
                                  Text('${item['quantity']}'),
                                  IconButton(
                                    icon: const Icon(Icons.add),
                                    onPressed: () {
                                      setDialogState(() {
                                        item['quantity'] += 1;
                                        setState(() {
                                          _calculateTotal();
                                        });
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
              ),
              actions: [
                if (_cart.isNotEmpty)
                  Text(
                    '总计: ¥${_totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontFamily: 'SimKai',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('关闭', style: TextStyle(fontFamily: 'SimKai')),
                ),
                if (_cart.isNotEmpty)
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _showCheckoutDialog();
                    },
                    child: const Text('结算', style: TextStyle(fontFamily: 'SimKai')),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  void _showCheckoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('订单确认', style: TextStyle(fontFamily: 'SimKai')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('感谢您的购买！', style: TextStyle(fontFamily: 'SimKai')),
              const SizedBox(height: 10),
              Text(
                '订单总额: ¥${_totalAmount.toStringAsFixed(2)}',
                style: const TextStyle(
                  fontFamily: 'SimKai',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                '我们将尽快为您安排发货，愿佛法加持，吉祥如意！',
                style: TextStyle(fontFamily: 'SimKai'),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _cart.clear();
                  _totalAmount = 0.0;
                });
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('订单提交成功！'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('确认订单', style: TextStyle(fontFamily: 'SimKai')),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentProducts = _products[_categories[_selectedCategoryIndex]] ?? [];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '般若梵心商城',
          style: TextStyle(
            fontFamily: 'SimKai',
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.brown[700],
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.shopping_cart, color: Colors.white),
                onPressed: _showCartDialog,
              ),
              if (_cart.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${_cart.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.brown[100]!,
              Colors.brown[50]!,
            ],
          ),
        ),
        child: Column(
          children: [
            // 分类选择
            Container(
              height: 60,
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final isSelected = index == _selectedCategoryIndex;
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedCategoryIndex = index;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.brown[700] : Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.brown[700]!,
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            _categories[index],
                            style: TextStyle(
                              fontFamily: 'SimKai',
                              color: isSelected ? Colors.white : Colors.brown[700],
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            // 商品列表
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.7,
                ),
                itemCount: currentProducts.length,
                itemBuilder: (context, index) {
                  final product = currentProducts[index];
                  return Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 3,
                          child: ClipRRect(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12),
                            ),
                            child: Image.asset(
                              product['image'],
                              width: double.infinity,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  product['name'],
                                  style: const TextStyle(
                                    fontFamily: 'SimKai',
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  product['description'],
                                  style: const TextStyle(
                                    fontFamily: 'SimKai',
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const Spacer(),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '¥${product['price'].toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () => _addToCart(product),
                                      child: Container(
                                        padding: const EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: Colors.brown[700],
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        child: const Icon(
                                          Icons.add_shopping_cart,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> with WidgetsBindingObserver {
  VideoPlayerController? _videoController;
  bool _isPlaying = false;
  bool _showVideo = false;
  bool _showExitHint = false;
  bool _showCalendar = false;
  bool _showSutraList = false;
  List<Map<String, dynamic>> _sutraList = [];
  String _currentTime = '';
  String _currentDate = '';
  String _currentWeekday = '';
  String _lunarDate = '';
  String _tibetanDate = '';
  String _tibetanFestival = '';
  String _tibetanMerit = '';
  String _chineseBuddhistFestival = '';
  late Timer _timer;
  late Timer _backgroundTimer;
  late Timer? _resetTimer;
  DateTime? _currentFocusedDay;
  final List<String> _backgroundImages = ['assets/wp001.png', 'assets/wp002.png', 'assets/wp003.png', 'assets/wp004.png', 'assets/wp005.png', 'assets/wp006.png'];
  int _currentBackgroundIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    _loadSutras();
    _startAutoNfcScan();
    _initializeVideo();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) => _updateTime());
    _backgroundTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      setState(() {
        int newIndex;
        do {
          newIndex = DateTime.now().millisecondsSinceEpoch % _backgroundImages.length;
        } while (newIndex == _currentBackgroundIndex);
        _currentBackgroundIndex = newIndex;
      });
    });
  }

  Future<void> _loadSutras() async {
    try {
      final String response = await rootBundle.loadString('assets/sutras.json');
      final data = await json.decode(response) as List;
      setState(() {
        _sutraList = data.map((item) => item as Map<String, dynamic>).toList();
      });
    } catch (e) {
      debugPrint('Error loading sutras: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      if (_videoController != null && _isPlaying) {
        setState(() {
          _videoController!.pause();
          _isPlaying = false;
          _showVideo = false;
        });
      }
    }
  }

  Future<void> _initializeVideo({String videoPath = 'assets/MP4/buddha_mantra.mp4'}) async {
    _videoController = VideoPlayerController.asset(videoPath);
    await _videoController!.initialize();
    _videoController!.addListener(() {
      if (_videoController!.value.position >= _videoController!.value.duration) {
        setState(() {
          _showVideo = false;
          _isPlaying = false;
        });
      }
    });
    setState(() {});
  }

  Future<String> _getChineseBuddhistFestival(String lunarMonth, String lunarDay) async {
    try {
      final festivalData = await rootBundle.loadString('assets/汉传佛教节日.csv');
      if (festivalData.isEmpty) {
        return '';
      }

      String cleanData = festivalData.replaceAll('\uFEFF', '');
      final lines = cleanData.split(RegExp(r'\r\n|\r|\n')).where((line) => line.isNotEmpty).toList();
      
      if (lines.isEmpty) {
        return '';
      }

      String targetDate = '$lunarMonth$lunarDay';

      Set<String> festivals = {};
      for (var line in lines) {
        final parts = line.split(RegExp(r',(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)'));
        if (parts.length >= 2) {
          final festivalDate = parts[0].trim().replaceAll('"', '');
          final festivalName = parts[1].trim().replaceAll('"', '');

          if (festivalDate == targetDate) {
            if (!festivals.contains(festivalName)) {
              festivals.add(festivalName);
            }
          }
        }
      }
      
      return festivals.isNotEmpty ? festivals.join('；') : '';
    } catch (e, stackTrace) {
      debugPrint('读取节日数据时出错：$e');
      debugPrint('错误堆栈：$stackTrace');
      return '';
    }
  }

  void _updateTime() async {
    final now = DateTime.now();
    final tibetanData = await TibetanCalendar.getTodayTibetanDate();
    final lunar = Lunar.fromDate(now);
    String lunarMonth = lunar.getMonthInChinese();
    String lunarDay = lunar.getDayInChinese();
    
    if (!lunarMonth.endsWith('月')) {
      lunarMonth = '${lunarMonth}月';
    }
    
    if (RegExp(r'^[一二三四五六七八九]$').hasMatch(lunarDay)) {
      lunarDay = '初$lunarDay';
    } else if (lunarDay == '十') {
      lunarDay = '初十';
    }
    
    final festivalResult = await _getChineseBuddhistFestival(lunarMonth, lunarDay);
    if (mounted) {
      setState(() {
        _currentTime = now.toString().substring(11, 19);
        _currentDate = '${now.year}年${now.month}月${now.day}日';
        _currentWeekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.weekday % 7];
        _lunarDate = '农历${lunar.getYearInGanZhi()}年${lunarMonth}${lunarDay}';
        final tibetanDateParts = tibetanData['tibetanDate'].toString().split('月');
        _tibetanDate = '藏历${tibetanDateParts[0]}月（${tibetanData['monthName']}）${tibetanDateParts[1]}';
        _chineseBuddhistFestival = festivalResult;
        _tibetanFestival = tibetanData['festival'] ?? '';
        _tibetanMerit = tibetanData['merit'] ?? '';
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _videoController?.dispose();
    _timer.cancel();
    _backgroundTimer.cancel();
    _resetTimer?.cancel();
    super.dispose();
  }

  Future<void> _startAutoNfcScan() async {
    while (true) {
      try {
        var tag = await FlutterNfcKit.poll();
        var ndefRecords = await FlutterNfcKit.readNDEFRecords();
        
        if (ndefRecords.isNotEmpty && ndefRecords[0].payload != null) {
          String recordData = String.fromCharCodes(ndefRecords[0].payload!);
          String videoPath = '';
          
          if (recordData.contains('hcs')) {
            videoPath = 'assets/MP4/buddha_mantra.mp4';
          } else if (recordData.contains('gypsxz')) {
            videoPath = 'assets/MP4/观音心咒对齐版.mp4';
          } else if (recordData.contains('wspsxz')) {
            videoPath = 'assets/MP4/智慧剑开.mp4';
          } else if (recordData.contains('xjxjxj')) {
            videoPath = 'assets/MP4/心经.mp4';
          }
          
          if (videoPath.isNotEmpty) {
            if (_videoController != null) {
              await _videoController!.dispose();
              _videoController = null;
            }
            await _initializeVideo(videoPath: videoPath);
            setState(() {
              _videoController!.play();
              _isPlaying = true;
              _showVideo = true;
              _showExitHint = true;
            });
            Timer(const Duration(seconds: 5), () {
              if (mounted) {
                setState(() {
                  _showExitHint = false;
                });
              }
            });
          }
        }

        await FlutterNfcKit.finish();
      } catch (e) {
        await FlutterNfcKit.finish();
      }
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  Future<List<Map<String, String>>> _getBuddhaImages() async {
    // 按照指定顺序返回佛菩萨列表
    // 第一行：药师佛、释迦摩尼佛、莲花生大士
    // 第二行：观音菩萨、阿弥陀佛、大势至菩萨  
    // 第三行：文殊菩萨、地藏王菩萨、普贤菩萨
    final List<Map<String, String>> deities = [
      {'name': '药师佛', 'image': 'assets/figure of the Buddha/药师佛.jpg'},
      {'name': '释迦摩尼佛', 'image': 'assets/figure of the Buddha/释迦摩尼佛.jpeg'},
      {'name': '莲花生大士', 'image': 'assets/figure of the Buddha/莲花生大士.jpg'},
      {'name': '观世音菩萨', 'image': 'assets/figure of the Buddha/观世音菩萨.jpg'},
      {'name': '阿弥陀佛', 'image': 'assets/figure of the Buddha/阿弥陀佛.jpeg'},
      {'name': '大势至菩萨', 'image': 'assets/figure of the Buddha/大势至菩萨.png'},
      {'name': '文殊菩萨', 'image': 'assets/figure of the Buddha/文殊菩萨.jpeg'},
      {'name': '地藏王菩萨', 'image': 'assets/figure of the Buddha/地藏王菩萨.jpeg'},
      {'name': '普贤菩萨', 'image': 'assets/figure of the Buddha/普贤菩萨.jpeg'},
    ];
    
    return deities;
  }

  void _showFullScreenBuddha(BuildContext context, String imagePath, String buddhaName) {
    showDialog(
      context: context,
      barrierColor: Colors.black,
      builder: (BuildContext context) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Stack(
              children: [
                // 全屏显示佛菩萨像
                Center(
                  child: InteractiveViewer(
                    child: Image.asset(
                      imagePath,
                      fit: BoxFit.contain,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.black,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.image_not_supported,
                                  color: Colors.white54,
                                  size: 64,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  '图片加载失败',
                                  style: TextStyle(
                                    color: Colors.white54,
                                    fontSize: 16,
                                    fontFamily: 'SimKai',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                // 底部显示佛菩萨名称
                Positioned(
                  bottom: 50,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                    child: Text(
                      buddhaName,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontFamily: 'SimKai',
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 3,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                // 右上角关闭按钮
                Positioned(
                  top: 50,
                  right: 20,
                  child: IconButton(
                    icon: Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 30,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showBuddhaGridDialog(BuildContext context) async {
    final List<Map<String, String>> deities = await _getBuddhaImages();

    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.all(10),
          child: Container(
            padding: EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (deities.isEmpty)
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Text(
                      '暂无佛菩萨像',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'SimKai',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                else
                  GridView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio: 0.7,
                    ),
                    itemCount: deities.length,
                    itemBuilder: (context, index) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                          child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop(); // 关闭当前对话框
                              _showFullScreenBuddha(context, deities[index]['image']!, deities[index]['name']!);
                            },
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.0),
                              child: Image.asset(
                                deities[index]['image']!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[300],
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey[600],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                          SizedBox(height: 5),
                          Text(
                            deities[index]['name']!,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'SimKai',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      );
                    },
                  ),
                SizedBox(height: 15),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.white, size: 30),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCircleButton(String text) {
    return GestureDetector(
      onTap: () {
        if (text == '历') {
          setState(() {
            _showCalendar = !_showCalendar;
          });
        } else if (text == '闻') {
          setState(() {
            _showSutraList = !_showSutraList;
          });
        } else if (text == '礼') {
          _showBuddhaGridDialog(context);
        } else if (text == '品') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ShopPage()),
          );
        }
      },
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontFamily: 'hyxk',
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSutraList() {
    return Positioned(
      bottom: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.5),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListView.builder(
              shrinkWrap: true,
              itemCount: _sutraList.length,
              itemBuilder: (context, index) {
                final sutra = _sutraList[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: OutlinedButton(
                    onPressed: () async {
                      print('按钮被点击: ${sutra['title']}');
                      setState(() {
                        _showSutraList = false;
                      });
                      
                      // 根据经文标题播放对应视频
                      String videoPath = '';
                      if (sutra['title'] == '《金刚般若波罗蜜经》') {
                        videoPath = 'assets/MP4/金刚经480p.mp4';
                      } else if (sutra['title'] == '《般若波罗蜜多心经》') {
                        videoPath = 'assets/MP4/心经.mp4';
                      }
                      
                      print('选择的视频路径: $videoPath');
                      
                      if (videoPath.isNotEmpty) {
                        try {
                          if (_videoController != null) {
                            await _videoController!.dispose();
                            _videoController = null;
                          }
                          print('开始初始化视频: $videoPath');
                          await _initializeVideo(videoPath: videoPath);
                          print('视频初始化完成，开始播放');
                          if (_videoController != null && _videoController!.value.isInitialized) {
                            setState(() {
                              _videoController!.play();
                              _isPlaying = true;
                              _showVideo = true;
                              _showExitHint = true;
                            });
                            print('视频开始播放');
                          } else {
                            print('视频控制器未正确初始化');
                          }
                          Timer(const Duration(seconds: 5), () {
                            if (mounted) {
                              setState(() {
                                _showExitHint = false;
                              });
                            }
                          });
                        } catch (e) {
                          print('视频播放出错: $e');
                        }
                      } else {
                        print('没有找到对应的视频文件');
                      }
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.white.withOpacity(0.7)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                    ),
                    child: Text(
                      sutra['title'] ?? '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'SimKai',
                        fontSize: 16,
                      ),
                    ),
                  ),
                );
              },
            ),
            SizedBox(height: 10),
            IconButton(
              icon: Icon(Icons.close, color: Colors.white),
              onPressed: () {
                setState(() {
                  _showSutraList = false;
                });
              },
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        fit: StackFit.expand,
        alignment: Alignment.center,
        children: [
          if (!_showVideo)
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: Colors.grey[850],
                image: DecorationImage(
                  image: AssetImage(_backgroundImages[_currentBackgroundIndex]),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          if (!_showVideo && _showCalendar)
            Positioned(
              top: 195,
              left: 0,
              right: 0,
              child: Container(
                height: 350,
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final availableHeight = constraints.maxHeight;
                    final headerHeight = 40.0;
                    final weekdayHeight = 20.0;
                    final availableRowSpace = availableHeight - headerHeight - weekdayHeight - 16;
                    final rowHeight = availableRowSpace / 6;
                    
                    return TableCalendar(
                      firstDay: DateTime.utc(2010, 10, 16),
                      lastDay: DateTime.utc(2030, 3, 14),
                      focusedDay: _currentFocusedDay ?? DateTime.now(),
                      onPageChanged: (focusedDay) {
                        setState(() {
                          _currentFocusedDay = focusedDay;
                          if (_resetTimer != null) {
                            _resetTimer!.cancel();
                          }
                          _resetTimer = Timer(const Duration(seconds: 10), () {
                            if (mounted) {
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                setState(() {
                                  _currentFocusedDay = DateTime.now();
                                  _resetTimer = null;
                                });
                              });
                            } else {
                              _resetTimer?.cancel();
                              _resetTimer = null;
                            }
                          });
                        });
                      },
                      rowHeight: rowHeight,
                      calendarStyle: const CalendarStyle(
                        defaultTextStyle: TextStyle(color: Colors.white),
                        weekendTextStyle: TextStyle(color: Colors.white),
                        holidayTextStyle: TextStyle(color: Colors.white),
                        selectedTextStyle: TextStyle(color: Colors.white),
                        todayTextStyle: TextStyle(color: Colors.white),
                        outsideTextStyle: TextStyle(color: Colors.white54),
                        todayDecoration: const BoxDecoration(
                         border: Border(top: const BorderSide(color: Colors.white, width: 1), bottom: const BorderSide(color: Colors.white, width: 1), left: const BorderSide(color: Colors.white, width: 1), right: const BorderSide(color: Colors.white, width: 1)),
                         shape: BoxShape.rectangle,
                        ),
                        cellMargin: EdgeInsets.all(2),
                      ),
                      headerStyle: HeaderStyle(
                        formatButtonVisible: false,
                        titleTextStyle: const TextStyle(color: Colors.white, fontFamily: 'SimKai'),
                        leftChevronIcon: const Icon(Icons.chevron_left, color: Colors.white),
                        rightChevronIcon: const Icon(Icons.chevron_right, color: Colors.white),
                        titleCentered: true,
                        titleTextFormatter: (date, locale) {
                          final months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
                          return ' ${date.year} ${months[date.month - 1]} ';
                        },
                        headerPadding: const EdgeInsets.symmetric(vertical: 4),
                      ),
                      daysOfWeekStyle: const DaysOfWeekStyle(
                        weekdayStyle: TextStyle(color: Colors.white),
                        weekendStyle: TextStyle(color: Colors.white),
                      ),
                      daysOfWeekHeight: 20,
                      calendarBuilders: CalendarBuilders(
                        dowBuilder: (context, day) {
                          return Center(
                            child: Text(
                              ['日', '一', '二', '三', '四', '五', '六'][day.weekday % 7],
                              style: const TextStyle(color: Colors.white, fontFamily: 'SimKai'),
                            ),
                          );
                        },
                        defaultBuilder: (context, date, _) {
                          final lunar = Lunar.fromDate(date);
                          return Container(
                            margin: const EdgeInsets.symmetric(vertical: 2),
                            alignment: Alignment.center,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '${date.day}',
                                  style: const TextStyle(color: Colors.white, fontSize: 14),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  lunar.getDayInChinese(),
                                  style: const TextStyle(color: Colors.white, fontSize: 10, fontFamily: 'SimKai', height: 1.0),
                                ),
                              ],
                            ),
                          );
                        },
                        todayBuilder: (context, date, _) {
                          final lunar = Lunar.fromDate(date);
                          return Container(
                            margin: const EdgeInsets.symmetric(vertical: 2),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white, width: 0.6),
                              shape: BoxShape.rectangle,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '${date.day}',
                                  style: const TextStyle(color: Colors.white, fontSize: 14),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  lunar.getDayInChinese(),
                                  style: const TextStyle(color: Colors.white, fontSize: 10, fontFamily: 'SimKai', height: 1.0),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),
            ),
          if (!_showVideo)
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Column(
                children: [
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '$_currentDate $_currentWeekday',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontFamily: 'SimKai',
                            color: Colors.white,
                            fontSize: 16,
                            shadows: [
                              Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(2, 2), blurRadius: 8),
                            ],
                          ),
                        ),
                        SizedBox(height: 1),
                        Text(
                          _currentTime,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontFamily: 'SimKai',
                            color: Colors.white,
                            fontSize: 42,
                            shadows: [
                              Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(4, 4), blurRadius: 15),
                              Shadow(color: const Color.fromARGB(255, 63, 63, 63), offset: Offset(2, 2), blurRadius: 8),
                            ],
                          ),
                        ),
                        SizedBox(height: 1),
                        Column(
                          children: [
                            Text(
                              _lunarDate,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontFamily: 'SimKai',
                                color: Colors.white,
                                fontSize: 13,
                                shadows: [
                                  Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(2, 2), blurRadius: 8),
                                ],
                              ),
                            ),
                            SizedBox(height: 5),
                            Text(
                              _tibetanDate,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontFamily: 'SimKai',
                                color: Colors.white,
                                fontSize: 13,
                                shadows: [
                                  Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(2, 2), blurRadius: 8),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 15),
                  if (!_showCalendar)
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Text(
                            "今日提示",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontFamily: 'SimKai',
                              color: Colors.white,
                              fontSize: 15,
                              shadows: [
                                Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(2, 2), blurRadius: 8),
                              ],
                            ),
                          ),
                          SizedBox(height: 5),
                          if (_chineseBuddhistFestival.isNotEmpty)
                            Text(
                              _chineseBuddhistFestival,
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                fontFamily: 'SimKai',
                                color: Colors.white,
                                fontSize: 15,
                                shadows: [
                                  Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(2, 2), blurRadius: 8),
                                ],
                              ),
                            ),
                          SizedBox(height: 5),
                          if (_tibetanFestival.isNotEmpty)
                            Text(
                              '$_tibetanFestival（$_tibetanMerit）',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                fontFamily: 'SimKai',
                                color: Colors.white,
                                fontSize: 15,
                                shadows: [
                                  Shadow(color: const Color.fromARGB(255, 33, 33, 33), offset: Offset(2, 2), blurRadius: 8),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          if (_showVideo && _videoController != null && _videoController!.value.isInitialized)
            GestureDetector(
              onTapDown: (_) {
                setState(() {
                  _showExitHint = true;
                });
                Timer(const Duration(seconds: 5), () {
                  if (mounted) {
                    setState(() {
                      _showExitHint = false;
                    });
                  }
                });
              },
              onDoubleTap: () {
                setState(() {
                  _showVideo = false;
                  _isPlaying = false;
                  _showExitHint = false;
                  _videoController!.pause();
                });
              },
              child: Stack(
                children: [
                  FittedBox(
                    fit: BoxFit.cover,
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      child: AspectRatio(
                        aspectRatio: _videoController!.value.aspectRatio,
                        child: VideoPlayer(_videoController!),
                      ),
                    ),
                  ),
                  if (_showExitHint)
                    Positioned(
                      bottom: 50,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Text(
                          '双击退出播放',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontFamily: 'SimKai',
                            shadows: [
                              Shadow(color: Colors.black, offset: Offset(2, 2), blurRadius: 4),
                            ],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          if (!_showVideo)
            Positioned(
              bottom: 20,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildCircleButton('历'),
                    _buildCircleButton('礼'),
                    _buildCircleButton('闻'),
                    _buildCircleButton('品'),
                  ],
                ),
              ),
            ),
          if (_showSutraList) _buildSutraList(),
        ],
      ),
    );
  }
}