import 'package:flutter/services.dart' show rootBundle;
import 'package:csv/csv.dart';
import 'dart:convert';

class TibetanCalendar {
  static Future<Map<String, dynamic>> getTodayTibetanDate() async {
    try {
      // 确保资源文件存在
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = Map<String, dynamic>.from(json.decode(manifestContent));
      print('资源文件列表: ${manifestMap.keys.toList()}');
      
      if (!manifestMap.containsKey('assets/tibetan_calendar_all.csv')) {
        print('找不到藏历数据文件在资源列表中');
        throw Exception('藏历数据文件未包含在应用资源中');
      }

      // 读取CSV文件内容
      final content = await rootBundle.loadString(
        'assets/tibetan_calendar_all.csv',
        cache: false
      );
      print('CSV文件内容长度: ${content.length}');
      
      if (content.isEmpty) {
        throw Exception('藏历数据文件为空');
      }

      // 转换CSV数据
      final rows = const CsvToListConverter().convert(
        content,
        shouldParseNumbers: false
      );
      print('CSV行数: ${rows.length}');
      
      if (rows.isEmpty) {
        throw Exception('CSV数据解析后为空');
      }

      // 获取当前日期
      final now = DateTime.now();
      final today = '${now.year}年${now.month}月${now.day}日';
      print('当前日期: $today');
      
      // 查找匹配日期
      for (var i = 1; i < rows.length; i++) {
        if (rows[i][0] == today) {
          print('找到匹配日期: ${rows[i].toString()}');
          return {
            'tibetanDate': rows[i][1] ?? '',
            'monthName': rows[i][2] ?? '',
            'festival': rows[i][3] ?? '',
            'merit': rows[i][4] ?? '',
          };
        }
      }
      
      print('未找到对应日期: $today');
      return {
        'tibetanDate': '未找到对应日期',
        'monthName': '',
        'festival': '',
        'merit': '',
      };
    } catch (e) {
      print('藏历数据读取错误: $e');
      return {
        'tibetanDate': '数据读取错误',
        'monthName': '',
        'festival': '',
        'merit': '',
      };
    }
  }
}