# 般若梵心 (Prajna Heart) NFC Project

一个基于 Flutter 开发的移动应用，旨在为佛教修行者提供现代化的数字体验。应用通过集成NFC技术，将实体的NFC标签与丰富的多媒体佛教内容相关联，并提供详细的公历、农历和藏历信息。

## ✨ 主要功能

- **NFC互动播放**：通过手机NFC功能扫描预设的NFC标签，即可自动触发播放相应的佛教经文或咒语视频，实现物理世界与数字内容的无缝连接。
- **动态主页**：
    - **实时时间显示**：精确显示公历、农历及藏历日期和时间。
    - **节日提醒**：根据内置的CSV数据，自动提示重要的汉传佛教及藏传佛教节日。
    - **动态背景**：背景图片定时自动切换，营造宁静祥和的氛围。
- **多功能日历**：
    - 集成一个可切换的日历视图，同时展示公历和农历日期。
- **视频播放器**：
    - 内置全屏视频播放器，用于展示扫描NFC标签后触发的视频内容。
    - 支持双击屏幕快速退出播放。
- **启动画面**：应用启动时展示随机的精美图片，提升初次打开的体验。

## 🛠️ 技术栈

- **框架**: [Flutter](https://flutter.dev/)
- **核心依赖**:
    - `flutter_nfc_kit`: 用于NFC标签的读取和轮询。
    - `video_player`: 用于播放应用内的视频资源。
    - `table_calendar`: 用于构建和展示日历视图。
    - `lunar`: 用于处理农历日期计算。
    - `csv`: 用于解析存储在 `assets` 中的节日数据。

## 📂 项目结构

```
nfc_project/
├── lib/
│   ├── main.dart           # 应用主入口和核心逻辑
│   └── tibetan_calendar.dart # 藏历数据处理
├── assets/
│   ├── *.mp4               # 经文和咒语视频文件
│   ├── *.png, *.jpg        # 背景、Logo等图片资源
│   ├── *.csv               # 汉传佛教和藏历节日数据
│   └── *.ttf               # 自定义字体文件
├── android/                # Android原生项目配置
├── ios/                    # iOS原生项目配置
└── pubspec.yaml            # 项目依赖和资源配置文件
```

## 🚀 如何运行

1.  **克隆或下载项目**

2.  **获取Flutter依赖包**:
    ```bash
    flutter pub get
    ```

3.  **运行应用**:
    ```bash
    flutter run
    ```

> **注意**:
> - 请确保您的设备支持NFC功能，以便测试NFC相关特性。
> - 应用的许多核心功能（如视频播放）依赖于预设的NFC标签。您需要准备包含特定载荷（如`hcs`, `gypsxz`等）的NFC标签进行测试。